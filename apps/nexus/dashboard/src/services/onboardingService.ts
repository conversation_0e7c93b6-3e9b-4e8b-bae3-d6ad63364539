import authService from './authService';

const apiClient = authService.getApiClient();

// Type definitions for API responses
interface Country {
  id: string;
  country_name: string;
}

interface Currency {
  id: string;
  currency_name: string;
  symbol: string;
}

interface Language {
  id: string;
  language_name: string;
}

interface CountriesResponse {
  success: boolean;
  specific_code: string;
  response_type: string;
  message: string;
  user_message: string;
  data: {
    countries: {
      countries: Country[];
    };
  };
}

interface CurrenciesResponse {
  success: boolean;
  specific_code: string;
  response_type: string;
  message: string;
  user_message: string;
  data: {
    currencies: {
      currencies: Currency[];
    };
  };
}

interface LanguagesResponse {
  success: boolean;
  specific_code: string;
  response_type: string;
  message: string;
  user_message: string;
  data: {
    languages: {
      languages: Language[];
    };
  };
}

interface RegistrationData {
  company_name: string;
  abbr: string;
  default_currency: string;
  country: string;
  email: string;
  first_name: string;
  last_name: string;
  password: string;
  language: string;
}

interface RegistrationResponse {
  success: boolean;
  specific_code: string;
  response_type: string;
  message: string;
  user_message: string;
  data: {
    user: {
      email: string;
      full_name: string;
      first_name: string;
      last_name: string;
      user_id: string;
      user_roles: string;
    };
    auth: {
      session_id: string;
    };
  };
}

/**
 * Fetch list of countries
 */
async function fetchCountries(): Promise<Country[]> {
  try {
    const response = await apiClient.get<CountriesResponse>('/api/v1/utilities/countries');
    return response.data.data.countries.countries;
  } catch (error) {
    console.error('Error fetching countries:', error);
    throw new Error('Failed to fetch countries');
  }
}

/**
 * Fetch list of currencies
 */
async function fetchCurrencies(): Promise<Currency[]> {
  try {
    const response = await apiClient.get<CurrenciesResponse>('/api/v1/utilities/currencies');
    return response.data.data.currencies.currencies;
  } catch (error) {
    console.error('Error fetching currencies:', error);
    throw new Error('Failed to fetch currencies');
  }
}

/**
 * Fetch list of languages
 */
async function fetchLanguages(): Promise<Language[]> {
  try {
    const response = await apiClient.get<LanguagesResponse>('/api/v1/utilities/languages');
    return response.data.data.languages.languages;
  } catch (error) {
    console.error('Error fetching languages:', error);
    throw new Error('Failed to fetch languages');
  }
}

/**
 * Register a new user
 */
async function registerUser(userData: RegistrationData): Promise<RegistrationResponse> {
  try {
    const response = await apiClient.post<RegistrationResponse>('/api/v1/onboarding/register', userData);
    return response.data;
  } catch (error) {
    console.error('Error registering user:', error);
    throw error;
  }
}

const onboardingService = {
  fetchCountries,
  fetchCurrencies,
  fetchLanguages,
  registerUser
};

export default onboardingService;
export type { Country, Currency, Language, RegistrationData, RegistrationResponse };
