<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useAuthStore } from "../../stores/auth";
import onboardingService from "../../services/onboardingService";
import Alert from "../ui/Alert.vue";
import Spinner from "../ui/Spinner.vue";
import LoadingState from "../LoadingState.vue";

const router = useRouter();
const authStore = useAuthStore();

// Form data
const formData = reactive({
	first_name: "",
	last_name: "",
	email: "",
	password: "",
	company_name: "",
	abbr: "",
	country: "",
	default_currency: "",
	language: "",
});

// Form state
const errors = reactive({});
const errorMessage = ref("");
const isLoadingData = ref(true);
const isSubmitting = ref(false);
const showPassword = ref(false);

// Step management
const currentStep = ref(1);
const totalSteps = 3;

const steps = [
	{
		id: 1,
		title: "Personal Information",
		description: "Tell us about yourself",
		icon: "user",
		fields: ["first_name", "last_name", "email", "password"],
	},
	{
		id: 2,
		title: "Company Details",
		description: "Setup your company",
		icon: "building",
		fields: ["company_name", "abbr"],
	},
	{
		id: 3,
		title: "Preferences",
		description: "Configure your settings",
		icon: "settings",
		fields: ["country", "default_currency", "language"],
	},
];

// Dropdown data
const countries = ref([]);
const currencies = ref([]);
const languages = ref([]);

// Load dropdown data on component mount
onMounted(async () => {
	try {
		isLoadingData.value = true;

		// Load all dropdown data in parallel
		const [countriesData, currenciesData, languagesData] = await Promise.all([
			onboardingService.fetchCountries(),
			onboardingService.fetchCurrencies(),
			onboardingService.fetchLanguages(),
		]);

		countries.value = countriesData;
		currencies.value = currenciesData;
		languages.value = languagesData;
	} catch (error) {
		console.error("Error loading form data:", error);
		errorMessage.value = "Failed to load form data. Please refresh the page.";
	} finally {
		isLoadingData.value = false;
	}
});

// Toggle password visibility
const togglePasswordVisibility = () => {
	showPassword.value = !showPassword.value;
};

// Step navigation functions
const nextStep = () => {
	if (validateCurrentStep() && currentStep.value < totalSteps) {
		currentStep.value++;
	}
};

const prevStep = () => {
	if (currentStep.value > 1) {
		currentStep.value--;
	}
};

const goToStep = (step) => {
	if (step >= 1 && step <= totalSteps) {
		currentStep.value = step;
	}
};

// Generate company abbreviation from company name
const generateAbbreviation = () => {
	if (formData.company_name && !formData.abbr) {
		const words = formData.company_name.trim().split(/\s+/);
		let abbr = "";

		if (words.length === 1) {
			// Single word: take first 2-3 characters
			abbr = words[0].substring(0, 3).toUpperCase();
		} else {
			// Multiple words: take first letter of each word (max 5)
			abbr = words
				.slice(0, 5)
				.map((word) => word.charAt(0))
				.join("")
				.toUpperCase();
		}

		formData.abbr = abbr;
	}
};

// Step validation
const validateCurrentStep = () => {
	// Clear previous errors for current step
	const currentStepFields = steps[currentStep.value - 1].fields;
	currentStepFields.forEach((field) => delete errors[field]);

	let isValid = true;

	// Validate fields for current step
	currentStepFields.forEach((field) => {
		if (!formData[field] || formData[field].trim() === "") {
			errors[field] = `${field.replace("_", " ")} is required`;
			isValid = false;
		}
	});

	// Additional validations
	if (currentStepFields.includes("email") && formData.email) {
		if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
			errors.email = "Please enter a valid email address";
			isValid = false;
		}
	}

	if (currentStepFields.includes("password") && formData.password) {
		if (formData.password.length < 8) {
			errors.password = "Password must be at least 8 characters long";
			isValid = false;
		}
	}

	if (currentStepFields.includes("abbr") && formData.abbr) {
		if (formData.abbr.length > 5) {
			errors.abbr = "Abbreviation must be 5 characters or less";
			isValid = false;
		}
	}

	return isValid;
};

// Form validation (for final submission)
const validateForm = () => {
	// Clear previous errors
	Object.keys(errors).forEach((key) => delete errors[key]);

	let isValid = true;

	// Required field validation
	const requiredFields = [
		"first_name",
		"last_name",
		"email",
		"password",
		"company_name",
		"abbr",
		"country",
		"default_currency",
		"language",
	];

	requiredFields.forEach((field) => {
		if (!formData[field] || formData[field].trim() === "") {
			errors[field] = `${field.replace("_", " ")} is required`;
			isValid = false;
		}
	});

	// Email validation
	if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
		errors.email = "Please enter a valid email address";
		isValid = false;
	}

	// Password validation
	if (formData.password && formData.password.length < 8) {
		errors.password = "Password must be at least 8 characters long";
		isValid = false;
	}

	// Abbreviation validation
	if (formData.abbr && formData.abbr.length > 5) {
		errors.abbr = "Abbreviation must be 5 characters or less";
		isValid = false;
	}

	return isValid;
};

// Handle form submission
const handleRegistration = async () => {
	if (!validateForm()) {
		return;
	}

	isSubmitting.value = true;
	errorMessage.value = "";

	try {
		// Step 1: Register the user
		const response = await onboardingService.registerUser(formData);

		if (response.success) {
			// Step 2: Login the user using existing login method
			const loginSuccess = await authStore.login(formData.email, formData.password);

			if (loginSuccess) {
				// Redirect to dashboard
				router.push("/dashboard");
			} else {
				errorMessage.value = "Registration successful, but login failed. Please try logging in manually.";
			}
		} else {
			errorMessage.value = response.user_message || "Registration failed. Please try again.";
		}
	} catch (error) {
		console.error("Registration error:", error);

		// Handle specific error responses
		if (error.response?.data?.user_message) {
			errorMessage.value = error.response.data.user_message;
		} else if (error.response?.data?.message) {
			errorMessage.value = error.response.data.message;
		} else {
			errorMessage.value =
				"Registration failed. Please check your information and try again.";
		}
	} finally {
		isSubmitting.value = false;
	}
};
</script>

<template>
	<div
		class="bg-white dark:bg-gray-900 p-8 rounded-xl shadow-2xl max-w-4xl w-full border border-gray-200 dark:border-gray-800"
	>
		<!-- Logo and Header -->
		<div class="text-center mb-8">
			<div class="flex justify-center mb-4">
				<img src="/logo.png" alt="Nexus Logo" class="h-12 w-auto" />
			</div>
			<h1 class="text-2xl font-bold text-gray-900 dark:text-white">Create Your Account</h1>
			<p class="text-gray-600 dark:text-gray-400 mt-2">
				Join Nexus and start managing your business
			</p>
		</div>

		<!-- Progress Steps -->
		<div class="mb-8">
			<div class="flex items-center justify-between">
				<div
					v-for="(step, index) in steps"
					:key="step.id"
					class="flex items-center"
					:class="{ 'flex-1': index < steps.length - 1 }"
				>
					<!-- Step Circle -->
					<div class="flex items-center">
						<div
							class="flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300"
							:class="{
								'bg-blue-600 border-blue-600 text-white': currentStep >= step.id,
								'border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400':
									currentStep < step.id,
								'bg-blue-100 dark:bg-blue-900 border-blue-600 text-blue-600 dark:text-blue-400':
									currentStep === step.id,
							}"
						>
							<!-- Step Icon -->
							<svg
								v-if="step.icon === 'user'"
								class="w-5 h-5"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
								/>
							</svg>
							<svg
								v-else-if="step.icon === 'building'"
								class="w-5 h-5"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
								/>
							</svg>
							<svg
								v-else-if="step.icon === 'settings'"
								class="w-5 h-5"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
								/>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
								/>
							</svg>
							<span v-else class="text-sm font-medium">{{ step.id }}</span>
						</div>

						<!-- Step Info -->
						<div class="ml-3 hidden sm:block">
							<p
								class="text-sm font-medium"
								:class="{
									'text-blue-600 dark:text-blue-400': currentStep === step.id,
									'text-gray-900 dark:text-white': currentStep > step.id,
									'text-gray-500 dark:text-gray-400': currentStep < step.id,
								}"
							>
								{{ step.title }}
							</p>
							<p class="text-xs text-gray-500 dark:text-gray-400">
								{{ step.description }}
							</p>
						</div>
					</div>

					<!-- Connector Line -->
					<div
						v-if="index < steps.length - 1"
						class="flex-1 h-0.5 mx-4 transition-all duration-300"
						:class="{
							'bg-blue-600': currentStep > step.id,
							'bg-gray-300 dark:bg-gray-600': currentStep <= step.id,
						}"
					></div>
				</div>
			</div>
		</div>

		<!-- Error Alert -->
		<Alert v-if="errorMessage" variant="destructive" dismissible class="mb-6">
			<div class="flex items-center gap-2">
				<svg
					xmlns="http://www.w3.org/2000/svg"
					width="16"
					height="16"
					viewBox="0 0 24 24"
					fill="none"
					stroke="currentColor"
					stroke-width="2"
					stroke-linecap="round"
					stroke-linejoin="round"
					class="lucide lucide-alert-circle"
				>
					<circle cx="12" cy="12" r="10" />
					<line x1="12" x2="12" y1="8" y2="12" />
					<line x1="12" x2="12.01" y1="16" y2="16" />
				</svg>
				<span>{{ errorMessage }}</span>
			</div>
		</Alert>

		<!-- Loading State -->
		<LoadingState v-if="isLoadingData" message="Loading form data..." />

		<!-- Step-based Registration Form -->
		<div v-else class="min-h-[400px]">
			<!-- Step Content Container -->
			<div class="transition-all duration-300 ease-in-out">
				<!-- Step 1: Personal Information -->
				<div v-if="currentStep === 1" class="space-y-6">
					<div class="text-center mb-6">
						<h2 class="text-xl font-semibold text-gray-900 dark:text-white">
							{{ steps[0].title }}
						</h2>
						<p class="text-gray-600 dark:text-gray-400 mt-1">
							{{ steps[0].description }}
						</p>
					</div>

					<div class="space-y-4">
						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<!-- First Name -->
							<div class="space-y-2">
								<label
									for="firstName"
									class="text-sm font-medium text-gray-700 dark:text-gray-300"
								>
									First Name *
								</label>
								<input
									id="firstName"
									v-model="formData.first_name"
									type="text"
									placeholder="Enter your first name"
									class="w-full px-4 py-3 border rounded-lg bg-gray-50 dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-all duration-200"
									:class="{
										'border-red-500 dark:border-red-500 ring-red-500 dark:ring-red-500':
											errors.first_name,
									}"
								/>
								<p
									v-if="errors.first_name"
									class="text-sm text-red-500 dark:text-red-400"
								>
									{{ errors.first_name }}
								</p>
							</div>

							<!-- Last Name -->
							<div class="space-y-2">
								<label
									for="lastName"
									class="text-sm font-medium text-gray-700 dark:text-gray-300"
								>
									Last Name *
								</label>
								<input
									id="lastName"
									v-model="formData.last_name"
									type="text"
									placeholder="Enter your last name"
									class="w-full px-4 py-3 border rounded-lg bg-gray-50 dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-all duration-200"
									:class="{
										'border-red-500 dark:border-red-500 ring-red-500 dark:ring-red-500':
											errors.last_name,
									}"
								/>
								<p
									v-if="errors.last_name"
									class="text-sm text-red-500 dark:text-red-400"
								>
									{{ errors.last_name }}
								</p>
							</div>
						</div>

						<!-- Email -->
						<div class="space-y-2">
							<label
								for="email"
								class="text-sm font-medium text-gray-700 dark:text-gray-300"
							>
								Email Address *
							</label>
							<input
								id="email"
								v-model="formData.email"
								type="email"
								placeholder="Enter your email address"
								class="w-full px-4 py-3 border rounded-lg bg-gray-50 dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-all duration-200"
								:class="{
									'border-red-500 dark:border-red-500 ring-red-500 dark:ring-red-500':
										errors.email,
								}"
							/>
							<p v-if="errors.email" class="text-sm text-red-500 dark:text-red-400">
								{{ errors.email }}
							</p>
						</div>

						<!-- Password -->
						<div class="space-y-2">
							<label
								for="password"
								class="text-sm font-medium text-gray-700 dark:text-gray-300"
							>
								Password *
							</label>
							<div class="relative">
								<input
									id="password"
									v-model="formData.password"
									:type="showPassword ? 'text' : 'password'"
									placeholder="Create a strong password"
									class="w-full px-4 pr-12 py-3 border rounded-lg bg-gray-50 dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-all duration-200"
									:class="{
										'border-red-500 dark:border-red-500 ring-red-500 dark:ring-red-500':
											errors.password,
									}"
								/>
								<button
									type="button"
									@click="togglePasswordVisibility"
									class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 focus:outline-none"
								>
									<svg
										v-if="showPassword"
										xmlns="http://www.w3.org/2000/svg"
										width="18"
										height="18"
										viewBox="0 0 24 24"
										fill="none"
										stroke="currentColor"
										stroke-width="2"
										stroke-linecap="round"
										stroke-linejoin="round"
									>
										<path d="M9.88 9.88a3 3 0 1 0 4.24 4.24" />
										<path
											d="M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68"
										/>
										<path
											d="M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61"
										/>
										<line x1="2" x2="22" y1="2" y2="22" />
									</svg>
									<svg
										v-else
										xmlns="http://www.w3.org/2000/svg"
										width="18"
										height="18"
										viewBox="0 0 24 24"
										fill="none"
										stroke="currentColor"
										stroke-width="2"
										stroke-linecap="round"
										stroke-linejoin="round"
									>
										<path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z" />
										<circle cx="12" cy="12" r="3" />
									</svg>
								</button>
							</div>
							<p
								v-if="errors.password"
								class="text-sm text-red-500 dark:text-red-400"
							>
								{{ errors.password }}
							</p>
							<p class="text-xs text-gray-500 dark:text-gray-400">
								Password must be at least 8 characters long
							</p>
						</div>
					</div>
				</div>

				<!-- Step 2: Company Information -->
				<div v-if="currentStep === 2" class="space-y-6">
					<div class="text-center mb-6">
						<h2 class="text-xl font-semibold text-gray-900 dark:text-white">
							{{ steps[1].title }}
						</h2>
						<p class="text-gray-600 dark:text-gray-400 mt-1">
							{{ steps[1].description }}
						</p>
					</div>

					<div class="space-y-4">
						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<!-- Company Name -->
							<div class="space-y-2">
								<label
									for="companyName"
									class="text-sm font-medium text-gray-700 dark:text-gray-300"
								>
									Company Name *
								</label>
								<input
									id="companyName"
									v-model="formData.company_name"
									type="text"
									placeholder="Enter your company name"
									@input="generateAbbreviation"
									class="w-full px-4 py-3 border rounded-lg bg-gray-50 dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-all duration-200"
									:class="{
										'border-red-500 dark:border-red-500 ring-red-500 dark:ring-red-500':
											errors.company_name,
									}"
								/>
								<p
									v-if="errors.company_name"
									class="text-sm text-red-500 dark:text-red-400"
								>
									{{ errors.company_name }}
								</p>
							</div>

							<!-- Company Abbreviation -->
							<div class="space-y-2">
								<label
									for="abbr"
									class="text-sm font-medium text-gray-700 dark:text-gray-300"
								>
									Abbreviation *
								</label>
								<input
									id="abbr"
									v-model="formData.abbr"
									type="text"
									placeholder="e.g., TC"
									maxlength="5"
									class="w-full px-4 py-3 border rounded-lg bg-gray-50 dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-all duration-200"
									:class="{
										'border-red-500 dark:border-red-500 ring-red-500 dark:ring-red-500':
											errors.abbr,
									}"
								/>
								<p
									v-if="errors.abbr"
									class="text-sm text-red-500 dark:text-red-400"
								>
									{{ errors.abbr }}
								</p>
								<p class="text-xs text-gray-500 dark:text-gray-400">
									Maximum 5 characters
								</p>
							</div>
						</div>
					</div>
				</div>

				<!-- Step 3: Preferences -->
				<div v-if="currentStep === 3" class="space-y-6">
					<div class="text-center mb-6">
						<h2 class="text-xl font-semibold text-gray-900 dark:text-white">
							{{ steps[2].title }}
						</h2>
						<p class="text-gray-600 dark:text-gray-400 mt-1">
							{{ steps[2].description }}
						</p>
					</div>

					<div class="space-y-4">
						<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
							<!-- Country -->
							<div class="space-y-2">
								<label
									for="country"
									class="text-sm font-medium text-gray-700 dark:text-gray-300"
								>
									Country *
								</label>
								<select
									id="country"
									v-model="formData.country"
									class="w-full px-4 py-3 border rounded-lg bg-gray-50 dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-all duration-200"
									:class="{
										'border-red-500 dark:border-red-500 ring-red-500 dark:ring-red-500':
											errors.country,
									}"
								>
									<option value="">Select Country</option>
									<option
										v-for="country in countries"
										:key="country.id"
										:value="country.id"
									>
										{{ country.country_name }}
									</option>
								</select>
								<p
									v-if="errors.country"
									class="text-sm text-red-500 dark:text-red-400"
								>
									{{ errors.country }}
								</p>
							</div>

							<!-- Currency -->
							<div class="space-y-2">
								<label
									for="currency"
									class="text-sm font-medium text-gray-700 dark:text-gray-300"
								>
									Currency *
								</label>
								<select
									id="currency"
									v-model="formData.default_currency"
									class="w-full px-4 py-3 border rounded-lg bg-gray-50 dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-all duration-200"
									:class="{
										'border-red-500 dark:border-red-500 ring-red-500 dark:ring-red-500':
											errors.default_currency,
									}"
								>
									<option value="">Select Currency</option>
									<option
										v-for="currency in currencies"
										:key="currency.id"
										:value="currency.id"
									>
										{{ currency.currency_name }} ({{ currency.symbol }})
									</option>
								</select>
								<p
									v-if="errors.default_currency"
									class="text-sm text-red-500 dark:text-red-400"
								>
									{{ errors.default_currency }}
								</p>
							</div>

							<!-- Language -->
							<div class="space-y-2">
								<label
									for="language"
									class="text-sm font-medium text-gray-700 dark:text-gray-300"
								>
									Language *
								</label>
								<select
									id="language"
									v-model="formData.language"
									class="w-full px-4 py-3 border rounded-lg bg-gray-50 dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-all duration-200"
									:class="{
										'border-red-500 dark:border-red-500 ring-red-500 dark:ring-red-500':
											errors.language,
									}"
								>
									<option value="">Select Language</option>
									<option
										v-for="language in languages"
										:key="language.id"
										:value="language.id"
									>
										{{ language.language_name }}
									</option>
								</select>
								<p
									v-if="errors.language"
									class="text-sm text-red-500 dark:text-red-400"
								>
									{{ errors.language }}
								</p>
							</div>
						</div>
					</div>
				</div>
				<!-- Navigation Buttons -->
				<div
					class="flex justify-between items-center mt-8 pt-6 border-t border-gray-200 dark:border-gray-700"
				>
					<!-- Previous Button -->
					<button
						v-if="currentStep > 1"
						@click="prevStep"
						type="button"
						class="flex items-center px-6 py-3 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg font-medium transition-all duration-200"
					>
						<svg
							class="w-4 h-4 mr-2"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M15 19l-7-7 7-7"
							/>
						</svg>
						Previous
					</button>
					<div v-else></div>

					<!-- Next/Submit Button -->
					<button
						v-if="currentStep < totalSteps"
						@click="nextStep"
						type="button"
						class="flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-all duration-200"
					>
						Next
						<svg
							class="w-4 h-4 ml-2"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M9 5l7 7-7 7"
							/>
						</svg>
					</button>
					<button
						v-else
						@click="handleRegistration"
						type="button"
						class="flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-all duration-200"
						:disabled="isSubmitting"
					>
						<Spinner v-if="isSubmitting" size="sm" class="mr-2 text-white" />
						<span v-if="isSubmitting">Creating Account...</span>
						<span v-else>Create Account</span>
					</button>
				</div>

				<!-- Login Link -->
				<div class="text-center mt-6">
					<p class="text-sm text-gray-600 dark:text-gray-400">
						Already have an account?
						<router-link
							to="/dashboard/login"
							class="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
						>
							Sign in
						</router-link>
					</p>
				</div>
			</div>
		</div>
	</div>
</template>
