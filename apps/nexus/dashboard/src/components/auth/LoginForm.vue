<template>
	<div
		class="bg-white dark:bg-gray-900 p-8 rounded-xl shadow-2xl max-w-md w-full border border-gray-200 dark:border-gray-800"
	>
		<!-- <PERSON><PERSON> and Header -->
		<div class="text-center mb-8">
			<div class="flex justify-center mb-4">
				<img src="/logo.png" alt="Nexus Logo" class="h-12 w-auto" />
			</div>
			<h1 class="text-2xl font-bold text-gray-900 dark:text-white">Welcome Back</h1>
			<p class="text-gray-600 dark:text-gray-400 mt-2">Sign in to your Nexus account</p>
		</div>

		<!-- Error Alert -->
		<Alert v-if="errorMessage" variant="destructive" dismissible class="mb-6">
			<div class="flex items-center gap-2">
				<svg
					xmlns="http://www.w3.org/2000/svg"
					width="16"
					height="16"
					viewBox="0 0 24 24"
					fill="none"
					stroke="currentColor"
					stroke-width="2"
					stroke-linecap="round"
					stroke-linejoin="round"
					class="lucide lucide-alert-circle"
				>
					<circle cx="12" cy="12" r="10" />
					<line x1="12" x2="12" y1="8" y2="12" />
					<line x1="12" x2="12.01" y1="16" y2="16" />
				</svg>
				<span>{{ errorMessage }}</span>
			</div>
		</Alert>

		<form @submit.prevent="handleLogin" class="space-y-5">
			<!-- Username Field -->
			<div class="space-y-2">
				<label for="username" class="text-sm font-medium text-gray-700 dark:text-gray-300"
					>Username</label
				>
				<div class="relative">
					<div
						class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none"
					>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							width="18"
							height="18"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							stroke-width="2"
							stroke-linecap="round"
							stroke-linejoin="round"
							class="text-gray-500 dark:text-gray-400"
						>
							<path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
							<circle cx="12" cy="7" r="4" />
						</svg>
					</div>
					<input
						id="username"
						v-model="username"
						type="text"
						placeholder="Enter your username"
						required
						class="w-full pl-10 pr-4 py-3 border rounded-lg bg-gray-50 dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-all duration-200"
						:class="{
							'border-red-500 dark:border-red-500 ring-red-500 dark:ring-red-500':
								usernameError,
						}"
					/>
				</div>
				<p v-if="usernameError" class="text-sm text-red-500 dark:text-red-400">
					{{ usernameError }}
				</p>
			</div>

			<!-- Password Field -->
			<div class="space-y-2">
				<label for="password" class="text-sm font-medium text-gray-700 dark:text-gray-300"
					>Password</label
				>
				<div class="relative">
					<div
						class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none"
					>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							width="18"
							height="18"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							stroke-width="2"
							stroke-linecap="round"
							stroke-linejoin="round"
							class="text-gray-500 dark:text-gray-400"
						>
							<rect width="18" height="11" x="3" y="11" rx="2" ry="2" />
							<path d="M7 11V7a5 5 0 0 1 10 0v4" />
						</svg>
					</div>
					<input
						id="password"
						v-model="password"
						:type="showPassword ? 'text' : 'password'"
						placeholder="Enter your password"
						required
						class="w-full pl-10 pr-12 py-3 border rounded-lg bg-gray-50 dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-all duration-200"
						:class="{
							'border-red-500 dark:border-red-500 ring-red-500 dark:ring-red-500':
								passwordError,
						}"
					/>
					<button
						type="button"
						@click="togglePasswordVisibility"
						class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 focus:outline-none"
					>
						<svg
							v-if="showPassword"
							xmlns="http://www.w3.org/2000/svg"
							width="18"
							height="18"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							stroke-width="2"
							stroke-linecap="round"
							stroke-linejoin="round"
						>
							<path d="M9.88 9.88a3 3 0 1 0 4.24 4.24" />
							<path
								d="M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68"
							/>
							<path
								d="M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61"
							/>
							<line x1="2" x2="22" y1="2" y2="22" />
						</svg>
						<svg
							v-else
							xmlns="http://www.w3.org/2000/svg"
							width="18"
							height="18"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							stroke-width="2"
							stroke-linecap="round"
							stroke-linejoin="round"
						>
							<path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z" />
							<circle cx="12" cy="12" r="3" />
						</svg>
					</button>
				</div>
				<p v-if="passwordError" class="text-sm text-red-500 dark:text-red-400">
					{{ passwordError }}
				</p>
			</div>

			<!-- Remember Me & Forgot Password -->
			<div class="flex items-center justify-between">
				<div class="flex items-center">
					<input
						id="remember-me"
						type="checkbox"
						v-model="rememberMe"
						class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
					/>
					<label
						for="remember-me"
						class="ml-2 block text-sm text-gray-700 dark:text-gray-300"
						>Remember me</label
					>
				</div>
				<a
					href="#"
					class="text-sm font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
					>Forgot password?</a
				>
			</div>

			<!-- Sign In Button -->
			<button
				type="submit"
				class="w-full py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 flex items-center justify-center"
				:disabled="isLoading"
			>
				<Spinner v-if="isLoading" size="sm" class="mr-2 text-white" />
				<span v-if="isLoading">Signing in...</span>
				<span v-else>Sign in</span>
			</button>
		</form>

		<!-- Divider -->
		<div class="relative my-6">
			<div class="absolute inset-0 flex items-center">
				<div class="w-full border-t border-gray-300 dark:border-gray-600"></div>
			</div>
			<div class="relative flex justify-center text-sm">
				<span class="px-2 bg-white dark:bg-gray-900 text-gray-500 dark:text-gray-400"
					>New to Nexus?</span
				>
			</div>
		</div>

		<!-- Create Account Button -->
		<router-link
			to="/dashboard/register"
			class="w-full py-3 px-4 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 text-gray-900 dark:text-white font-medium rounded-lg border border-gray-300 dark:border-gray-600 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200 flex items-center justify-center"
		>
			<svg
				xmlns="http://www.w3.org/2000/svg"
				width="18"
				height="18"
				viewBox="0 0 24 24"
				fill="none"
				stroke="currentColor"
				stroke-width="2"
				stroke-linecap="round"
				stroke-linejoin="round"
				class="mr-2"
			>
				<path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
				<circle cx="9" cy="7" r="4" />
				<line x1="19" x2="19" y1="8" y2="14" />
				<line x1="22" x2="16" y1="11" y2="11" />
			</svg>
			Create Account
		</router-link>
	</div>
</template>

<script setup>
import { ref } from "vue";
import { useRouter } from "vue-router";
import { useAuthStore } from "../../stores/auth";
import Alert from "../ui/Alert.vue";
import Spinner from "../ui/Spinner.vue";

const router = useRouter();
const authStore = useAuthStore();

const username = ref("");
const password = ref("");
const usernameError = ref("");
const passwordError = ref("");
const errorMessage = ref("");
const isLoading = ref(false);
const showPassword = ref(false);
const rememberMe = ref(false);

const togglePasswordVisibility = () => {
	showPassword.value = !showPassword.value;
};

const validateForm = () => {
	let isValid = true;
	usernameError.value = "";
	passwordError.value = "";

	if (!username.value) {
		usernameError.value = "Username is required";
		isValid = false;
	}

	if (!password.value) {
		passwordError.value = "Password is required";
		isValid = false;
	}

	return isValid;
};

const handleLogin = async () => {
	if (!validateForm()) {
		return;
	}

	isLoading.value = true;
	errorMessage.value = "";

	try {
		const success = await authStore.login(username.value, password.value);
		if (success) {
			router.push("/dashboard");
		} else {
			errorMessage.value = "Login failed. Please check your credentials.";
		}
	} catch (error) {
		errorMessage.value = error.message || "Failed to sign in. Please check your credentials.";
	} finally {
		isLoading.value = false;
	}
};
</script>
