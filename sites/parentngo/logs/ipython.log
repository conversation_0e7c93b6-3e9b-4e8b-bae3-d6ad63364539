2025-05-04 17:29:58,988 INFO ipython === bench console session ===
2025-05-04 17:29:58,988 INFO ipython frappe.utils.nowdate()
2025-05-04 17:29:58,988 INFO ipython frappe.datetime.get_day_diff("2025-03-11","2025-03-11")
2025-05-04 17:29:58,989 INFO ipython from frappe.utils import date_diff
2025-05-04 17:29:58,989 INFO ipython date_diff("2025-03-11","2025-03-11")
2025-05-04 17:29:58,989 INFO ipython date_diff("2025-03-11","2025-03-12")
2025-05-04 17:29:58,989 INFO ipython date_diff("2025-03-13","2025-03-12")
2025-05-04 17:29:58,989 INFO ipython === session end ===
2025-05-11 14:57:57,785 INFO ipython === bench console session ===
2025-05-11 14:57:57,787 INFO ipython === session end ===
2025-05-22 14:40:14,717 INFO ipython === bench console session ===
2025-05-22 14:40:14,719 INFO ipython frappe.db.exists("DocType", "Customer NGO Attachments")
2025-05-22 14:40:14,719 INFO ipython frappe.db.exists("DocType", "Customer NGO Attachmentsd")
2025-05-22 14:40:14,719 INFO ipython print(frappe.db.exists("DocType", "Customer NGO Attachments"))
2025-05-22 14:40:14,719 INFO ipython print(frappe.db.exists("DocType", "Customer NGO Attachmentsd"))
2025-05-22 14:40:14,719 INFO ipython === session end ===
2025-05-22 15:41:34,882 INFO ipython === bench console session ===
2025-05-22 15:41:34,883 INFO ipython frappe.db.table_exists("Customer NGO Attachments")
2025-05-22 15:41:34,883 INFO ipython from frappe.utils.install import sync_tables
2025-05-22 15:41:34,884 INFO ipython === session end ===
2025-05-22 16:05:30,925 INFO ipython === bench console session ===
2025-05-22 16:05:30,926 INFO ipython frappe.db.truncate("Has Role")
2025-05-22 16:05:30,926 INFO ipython frappe.db.commit()
2025-05-22 16:05:30,926 INFO ipython === session end ===
2025-06-10 22:02:52,247 INFO ipython === bench console session ===
2025-06-10 22:02:52,247 INFO ipython frappe.get_doc("LR Board Member Details","bb6c393hi8")
2025-06-10 22:02:52,248 INFO ipython doc = frappe.get_doc("LR Board Member Details","bb6c393hi8")
2025-06-10 22:02:52,248 INFO ipython doc.name
2025-06-10 22:02:52,248 INFO ipython doc = frappe.get_doc("Loan Request","LR-55")
2025-06-10 22:02:52,248 INFO ipython doc.board_members
2025-06-10 22:02:52,248 INFO ipython doc.board_members.doctype
2025-06-10 22:02:52,248 INFO ipython doc.board_members[0]
2025-06-10 22:02:52,248 INFO ipython doc.board_members[0].doctype
2025-06-10 22:02:52,248 INFO ipython doc.board_members[0].name
2025-06-10 22:02:52,248 INFO ipython help(doc.board_members[0])
2025-06-10 22:02:52,248 INFO ipython doc = frappe.get_doc("Loan Request","LR-56")
2025-06-10 22:02:52,248 INFO ipython doc = frappe.get_doc("Loan Request","LR-56")
2025-06-10 22:02:52,248 INFO ipython === session end ===
2025-06-10 22:03:38,121 INFO ipython === bench console session ===
2025-06-10 22:03:38,122 INFO ipython doc = frappe.get_doc("Loan Request","LR-56")
2025-06-10 22:03:38,122 INFO ipython doc.board_members[0].name
2025-06-10 22:03:38,122 INFO ipython doc.board_members[0].parent
2025-06-10 22:03:38,122 INFO ipython === session end ===
2025-06-18 14:13:02,992 INFO ipython === bench console session ===
2025-06-18 14:13:02,993 INFO ipython import frappe
2025-06-18 14:13:02,993 INFO ipython frappe.db.get_single_value("System Settings", "reset_password_link_expiry_duration")
2025-06-18 14:13:02,993 INFO ipython # Check for users with reset password keys
2025-06-18 14:13:02,993 INFO ipython frappe.db.sql("SELECT name, reset_password_key, last_reset_password_key_generated_on FROM tabUser WHERE reset_password_key IS NOT NULL AND reset_password_key != ''", as_dict=True)
2025-06-18 14:13:02,993 INFO ipython from frappe.utils import now_datetime
2025-06-18 14:13:02,993 INFO ipython from datetime import timedelta
2025-06-18 14:13:02,993 INFO ipython current_time = now_datetime()
2025-06-18 14:13:02,993 INFO ipython expiry_duration = 172800  # seconds (48 hours)
2025-06-18 14:13:02,994 INFO ipython print(f"Current time: {current_time}")
2025-06-18 14:13:02,994 INFO ipython print(f"Expiry duration: {expiry_duration} seconds ({expiry_duration/3600} hours)")
2025-06-18 14:13:02,994 INFO ipython # Check which keys are expired
2025-06-18 14:13:02,994 INFO ipython for user in frappe.db.sql("SELECT name, reset_password_key, last_reset_password_key_generated_on FROM tabUser WHERE reset_password_key IS NOT NULL AND reset_password_key != ''", as_dict=True):
        key_time = user['last_reset_password_key_generated_on']
            expired = current_time > key_time + timedelta(seconds=expiry_duration)
2025-06-18 14:13:02,994 INFO ipython     print(f"{user['name']}: Generated {key_time}, Expired: {expired}")
2025-06-18 14:13:02,994 INFO ipython for user in frappe.db.sql("SELECT name, reset_password_key, last_reset_password_key_generated_on FROM tabUser WHERE reset_password_key IS NOT NULL AND reset_password_key != ''", as_dict=True):
        key_time = user['last_reset_password_key_generated_on']
            expired = current_time > key_time + timedelta(seconds=expiry_duration)
2025-06-18 14:13:02,994 INFO ipython     print(f"{user['name']}: Generated {key_time}, Expired: {expired}")
2025-06-18 14:13:02,994 INFO ipython === session end ===
2025-06-22 14:23:14,329 INFO ipython === bench console session ===
2025-06-22 14:23:14,331 INFO ipython === session end ===
2025-06-22 14:38:16,069 INFO ipython === bench console session ===
2025-06-22 14:38:16,070 INFO ipython frappe.db.truncate("Has Role")
2025-06-22 14:38:16,070 INFO ipython frappe.db.commit()
2025-06-22 14:38:16,070 INFO ipython === session end ===
2025-07-20 15:22:54,647 INFO ipython === bench console session ===
2025-07-20 15:22:54,650 INFO ipython fields = ["bod_meetings_record", "ga_meetings_record", "ms_record", "visits_record", "bwl_record", "donations_record", "cash_receipts", "bank_deposits", "exchange_notes", "cheque_permits", "bookkeeping", "daily_journals", "ti_payments", "beneficiaries_portfolios", "daily_journals_records", "customer_record_book", "expenses_analytical_journal", "ie_journal", "treasury_journal", "bank_book", "assets_register", "budget_account", "re_account", "rp_account", "cash_flow_account", "draft_budget_account", "fa_report", "comment", "previous_works_status", "committee_decision", "cash_flow_form_status"]
for field in fields:
    print(frappe.meta.get_docfield("Financial Investigation", field).label)
    
2025-07-20 15:22:54,650 INFO ipython fields = ["bod_meetings_record", "ga_meetings_record", "ms_record", "visits_record", "bwl_record", "donations_record", "cash_receipts", "bank_deposits", "exchange_notes", "cheque_permits", "bookkeeping", "daily_journals", "ti_payments", "beneficiaries_portfolios", "daily_journals_records", "customer_record_book", "expenses_analytical_journal", "ie_journal", "treasury_journal", "bank_book", "assets_register", "budget_account", "re_account", "rp_account", "cash_flow_account", "draft_budget_account", "fa_report", "comment", "previous_works_status", "committee_decision", "cash_flow_form_status"]
for field in fields:
    meta = frappe.get_meta("Financial Investigation")
    label = meta.get_label(field)
    print(label)
    
2025-07-20 15:22:54,650 INFO ipython 
fields = ["daily_journals_records", "customer_record_book", "expenses_analytical_journal", "ie_journal", "treasury_journal", "bank_book", "assets_register"]
for field in fields:
    meta = frappe.get_meta("Financial Investigation")
    label = meta.get_label(field)
    print(label)
    
2025-07-20 15:22:54,650 INFO ipython === session end ===
