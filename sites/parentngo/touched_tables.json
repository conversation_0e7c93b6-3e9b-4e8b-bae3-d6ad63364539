["tabHas Role", "tabCustom DocPerm", "tabAPI", "tabWorkflow Action Master", "tabDocType Action", "tabDocPerm", "tabAttachment", "tabCustom Field", "tabWebsite", "tabEmail", "tabLoan Request", "tabParty", "tabDocType State", "tabWorkflow", "tabVersion", "tabFinancial Follow Up", "tabWorkspace Custom Block", "tabRole Workspace", "tabIAM Role", "tabFinancial Investigation", "tabWorkflow Transition", "tabFinancial", "tabLoan Disbursement Request", "tabHas", "tabWorkspace Shortcut", "tabInstalled Application", "tabCustom", "tabInstalled", "tabTop", "tabSeries", "tabRole", "tabWorkflow State", "tabDocField", "tabCustomer NGO", "tabPrint", "tabNavbar", "tabAPI Response", "tabNavbar <PERSON>em", "tabPortal Menu Item", "tabRole Profile", "tabTop Bar Item", "tabWorkspace", "tabWorkflow Document State", "tabLR", "tabWorkspace Chart", "tabAdministrative", "tabDocType Link", "tabCustomer", "tabAdministrative Follow Up", "tabIAM", "tabWorkspace Quick List", "tabWebsite Route Redirect", "tabParty Type", "tabCustom Role", "tabPolicy", "tabEmail Template", "tabProperty", "tabProperty Setter", "tabWorkspace Number Card", "tabAdministrative Investigation", "tabLoan", "tabList", "tabPortal", "tabList View Settings", "tabPrint Heading", "tabAPI Response Target", "tabWorkspace Link", "tabLR Board Member Details", "tabSingles", "tabTag Link", "tabDocType", "tabTag"]