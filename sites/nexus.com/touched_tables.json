["tabPatch Log", "tabProject", "tabAPI Response", "tabPick List", "tabDocField", "tabAPI Response Target", "tabSingles", "tabSerial", "tabInstalled Application", "tabCustom", "tabDashboard", "tabSales", "tabDelivery", "tabDocPerm", "tabWork Order", "tabMaterial Request", "tabDelivery Note Item", "tabPacked", "tabPick List Item", "tabPick", "tabReport Column", "tabCustom Field", "tabScheduled", "tabAsset Repair Consumed Item", "tabScheduled Job Type", "tabAPI", "tabAsset Repair", "tabPOS Invoice", "tabPrint Heading", "tabPOS", "tabDocType", "tabSerial No", "tabRole", "tabCustom DocPerm", "tabLead", "tabDocType Action", "tabInstalled", "tabPortal", "tabAsset", "tabPacked <PERSON>em", "tabPrint", "tabAsset Movement", "tabPortal Menu Item", "tabReport Filter", "tabHas", "tabDelivery Note", "tabTag Link", "tabDashboard Chart", "tabDocType Link", "tabSales Invoice", "tabReport", "tabDocType State", "tabHas Role", "tabOpportunity", "tabTag", "tabEmployee", "tabMaterial", "tabWork Order Operation", "tabPOS Invoice Merge Log", "tabWork", "tabPatch"]