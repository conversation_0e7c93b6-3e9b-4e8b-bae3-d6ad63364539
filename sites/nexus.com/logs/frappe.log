2025-07-16 15:11:12,723 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-07-16', 'delivery_date': '2025-07-18', 'billing_address_id': 'mohamed amir-Billing', 'shipping_address_id': 'mohamed amir-Billing', 'contact_id': 'mohamed1545 amirec-mohamed amir', 'payment_method_id': 'Cheque', 'items': [{'item_code': 'SKU008', 'item_name': 'Backpack', 'qty': 1, 'rate': 500, 'warehouse': 'All Warehouses - AFD'}, {'item_code': 'SKU003', 'item_name': 'Book', 'qty': 1, 'rate': 500, 'warehouse': 'All Warehouses - AFD'}], 'notes': ''}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-16 15:21:37,767 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-28 16:15:57,567 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'cmd': 'nexus.api.v1.system.endpoints.get_default_currency'}
