2025-07-06 12:18:14,910 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for serviceplanner
2025-07-06 12:18:14,962 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for serviceplanner
2025-07-06 12:18:15,007 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for serviceplanner
2025-07-08 11:21:09,583 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for serviceplanner
2025-07-08 11:21:09,588 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for serviceplanner
2025-07-08 11:21:09,592 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for serviceplanner
2025-07-08 11:21:09,617 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for serviceplanner
2025-07-08 11:21:09,619 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for serviceplanner
2025-07-08 11:21:09,632 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for serviceplanner
2025-07-08 11:21:09,638 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for serviceplanner
2025-07-08 11:21:09,702 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for serviceplanner
2025-07-08 11:21:09,722 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for serviceplanner
2025-07-08 11:21:09,735 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for serviceplanner
2025-07-08 11:21:09,738 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for serviceplanner
2025-07-08 11:21:09,742 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for serviceplanner
2025-07-08 11:21:09,762 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for serviceplanner
2025-07-08 11:21:09,770 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for serviceplanner
2025-07-08 11:21:09,788 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for serviceplanner
2025-07-08 11:21:09,799 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for serviceplanner
2025-07-08 11:21:09,804 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for serviceplanner
2025-07-08 11:21:09,807 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for serviceplanner
2025-07-08 11:21:09,810 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for serviceplanner
2025-07-08 11:21:09,817 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for serviceplanner
2025-07-08 11:21:09,827 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for serviceplanner
2025-07-08 11:21:09,830 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for serviceplanner
2025-07-08 11:21:09,833 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for serviceplanner
2025-07-08 11:21:09,838 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for serviceplanner
2025-07-08 11:21:10,090 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for serviceplanner
2025-07-08 11:21:10,547 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for serviceplanner
2025-07-08 11:21:10,620 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for serviceplanner
2025-07-13 11:18:50,749 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for serviceplanner
2025-07-13 11:18:50,752 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for serviceplanner
2025-07-13 11:18:50,761 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for serviceplanner
2025-07-13 11:18:50,764 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for serviceplanner
2025-07-13 11:18:50,769 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for serviceplanner
2025-07-13 11:18:50,775 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for serviceplanner
2025-07-13 11:18:50,781 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for serviceplanner
2025-07-13 11:18:50,795 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for serviceplanner
2025-07-13 11:18:50,801 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for serviceplanner
2025-07-13 11:18:50,805 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for serviceplanner
2025-07-13 11:18:50,813 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for serviceplanner
2025-07-13 11:18:50,816 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for serviceplanner
2025-07-13 11:18:50,820 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for serviceplanner
2025-07-13 11:18:50,826 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for serviceplanner
2025-07-13 11:18:50,831 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for serviceplanner
2025-07-13 11:18:50,851 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for serviceplanner
2025-07-13 11:18:50,860 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for serviceplanner
2025-07-13 11:18:50,869 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for serviceplanner
2025-07-13 11:18:50,872 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for serviceplanner
2025-07-13 11:18:50,874 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for serviceplanner
2025-07-13 11:18:50,878 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for serviceplanner
2025-07-13 11:18:50,901 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for serviceplanner
2025-07-13 11:18:50,907 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for serviceplanner
2025-07-13 11:18:50,913 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for serviceplanner
2025-07-13 11:18:50,916 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for serviceplanner
2025-07-13 11:18:50,925 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for serviceplanner
2025-07-13 11:18:50,933 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for serviceplanner
2025-07-13 11:18:50,936 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for serviceplanner
2025-07-13 11:18:50,942 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for serviceplanner
2025-07-13 11:18:50,955 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for serviceplanner
2025-07-13 11:18:50,961 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for serviceplanner
2025-07-13 11:18:50,968 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for serviceplanner
2025-07-13 11:18:50,971 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for serviceplanner
2025-07-13 11:18:50,974 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for serviceplanner
2025-07-15 10:51:52,204 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for serviceplanner
2025-07-15 10:51:52,212 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for serviceplanner
2025-07-15 10:51:52,214 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for serviceplanner
2025-07-15 10:51:52,216 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for serviceplanner
2025-07-15 10:51:52,229 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for serviceplanner
2025-07-15 10:51:52,231 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for serviceplanner
2025-07-15 10:51:52,233 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for serviceplanner
2025-07-15 10:51:52,235 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for serviceplanner
2025-07-15 10:51:52,237 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for serviceplanner
2025-07-15 10:51:52,239 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for serviceplanner
2025-07-15 10:51:52,247 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for serviceplanner
2025-07-15 10:51:52,253 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for serviceplanner
2025-07-15 10:51:52,255 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for serviceplanner
2025-07-15 10:51:52,258 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for serviceplanner
2025-07-15 10:51:52,266 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for serviceplanner
2025-07-15 10:51:52,271 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for serviceplanner
2025-07-15 10:51:52,273 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for serviceplanner
2025-07-15 10:51:52,287 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for serviceplanner
2025-07-15 10:51:52,289 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for serviceplanner
2025-07-15 10:51:52,301 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for serviceplanner
2025-07-15 10:51:52,304 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for serviceplanner
2025-07-15 10:51:52,307 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for serviceplanner
2025-07-15 10:51:52,314 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for serviceplanner
2025-07-15 10:51:52,318 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for serviceplanner
2025-07-15 10:51:52,325 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for serviceplanner
2025-07-15 10:51:52,334 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for serviceplanner
2025-07-15 10:51:52,338 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for serviceplanner
2025-07-17 17:13:29,129 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for serviceplanner
2025-07-17 17:13:29,136 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for serviceplanner
2025-07-17 17:13:29,139 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for serviceplanner
2025-07-17 17:13:29,143 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for serviceplanner
2025-07-17 17:13:29,151 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for serviceplanner
2025-07-17 17:13:29,153 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for serviceplanner
2025-07-17 17:13:29,164 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for serviceplanner
2025-07-17 17:13:29,169 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for serviceplanner
2025-07-17 17:13:29,172 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for serviceplanner
2025-07-17 17:13:29,174 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for serviceplanner
2025-07-17 17:13:29,177 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for serviceplanner
2025-07-17 17:13:29,189 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for serviceplanner
2025-07-17 17:13:29,232 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for serviceplanner
2025-07-17 17:13:29,247 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for serviceplanner
2025-07-17 17:13:29,251 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for serviceplanner
2025-07-17 17:13:29,255 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for serviceplanner
2025-07-27 11:29:07,634 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for serviceplanner
2025-07-27 11:29:07,639 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for serviceplanner
2025-07-27 11:29:07,642 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for serviceplanner
2025-07-27 11:29:07,650 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for serviceplanner
2025-07-27 11:29:07,661 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for serviceplanner
2025-07-27 11:29:07,663 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for serviceplanner
2025-07-27 11:29:07,671 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for serviceplanner
2025-07-27 11:29:07,674 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for serviceplanner
2025-07-27 11:29:07,676 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for serviceplanner
2025-07-27 11:29:07,700 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for serviceplanner
2025-07-27 11:29:07,702 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for serviceplanner
2025-07-27 11:29:07,707 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for serviceplanner
2025-07-27 11:29:07,710 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for serviceplanner
2025-07-27 11:29:07,712 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for serviceplanner
2025-07-27 11:29:07,722 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for serviceplanner
