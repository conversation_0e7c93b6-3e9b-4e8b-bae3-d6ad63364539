2025-07-14 12:32:33,486 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer_name': 'mohamed amir', 'transaction_date': '2025-07-14', 'delivery_date': '2025-07-24', 'billing_address': {'name': 'mohamed amir-Billing', 'address_line1': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607', 'city': 'zagazig', 'country': 'Egypt', 'pincode': '451147', 'is_primary_address': 1, 'is_shipping_address': 1, 'address_type': 'Billing', 'formatted_display': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607, zagazig, Egypt, 451147'}, 'shipping_address': {'name': 'mohamed amir-Billing', 'address_line1': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607', 'city': 'zagazig', 'country': 'Egypt', 'pincode': '451147', 'is_primary_address': 1, 'is_shipping_address': 1, 'address_type': 'Billing', 'formatted_display': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607, zagazig, Egypt, 451147'}, 'phone_number': '01001733918', 'payment_method': 'Cash', 'items': [{'item_code': 'SKU006', 'item_name': 'Coffee Mug', 'qty': 7, 'rate': 0}, {'item_code': 'SKU010', 'item_name': 'Camera', 'qty': 1, 'rate': 0}], 'notes': '', 'tags': 'New Order'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-14 13:03:04,792 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer_name': 'mohamed amir', 'transaction_date': '2025-07-14', 'delivery_date': '2025-07-15', 'billing_address': {'name': 'mohamed amir-Billing', 'address_line1': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607', 'city': 'zagazig', 'country': 'Egypt', 'pincode': '451147', 'is_primary_address': 1, 'is_shipping_address': 1, 'address_type': 'Billing', 'formatted_display': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607, zagazig, Egypt, 451147'}, 'shipping_address': {'name': 'mohamed amir-Billing', 'address_line1': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607', 'city': 'zagazig', 'country': 'Egypt', 'pincode': '451147', 'is_primary_address': 1, 'is_shipping_address': 1, 'address_type': 'Billing', 'formatted_display': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607, zagazig, Egypt, 451147'}, 'phone_number': '01001733918', 'payment_method': 'Credit Card', 'items': [{'item_code': 'SKU006', 'item_name': 'Coffee Mug', 'qty': 1, 'rate': 0}], 'notes': '', 'tags': 'New Order'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-14 13:05:05,559 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer_name': 'mohamed amir', 'transaction_date': '2025-07-14', 'delivery_date': '2025-07-30', 'billing_address': {'name': 'mohamed amir-Billing', 'address_line1': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607', 'city': 'zagazig', 'country': 'Egypt', 'pincode': '451147', 'is_primary_address': 1, 'is_shipping_address': 1, 'address_type': 'Billing', 'formatted_display': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607, zagazig, Egypt, 451147'}, 'shipping_address': {'name': 'mohamed amir-Billing', 'address_line1': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607', 'city': 'zagazig', 'country': 'Egypt', 'pincode': '451147', 'is_primary_address': 1, 'is_shipping_address': 1, 'address_type': 'Billing', 'formatted_display': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607, zagazig, Egypt, 451147'}, 'phone_number': '01001733918', 'payment_method': 'Cheque', 'items': [{'item_code': 'SKU006', 'item_name': 'Coffee Mug', 'qty': 1, 'rate': 0}], 'notes': '', 'tags': 'New Order'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-14 13:05:45,119 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer_name': 'mohamed amir', 'transaction_date': '2025-07-14', 'delivery_date': '2025-07-17', 'billing_address': {'name': 'mohamed amir-Billing', 'address_line1': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607', 'city': 'zagazig', 'country': 'Egypt', 'pincode': '451147', 'is_primary_address': 1, 'is_shipping_address': 1, 'address_type': 'Billing', 'formatted_display': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607, zagazig, Egypt, 451147'}, 'shipping_address': {'name': 'mohamed amir-Billing', 'address_line1': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607', 'city': 'zagazig', 'country': 'Egypt', 'pincode': '451147', 'is_primary_address': 1, 'is_shipping_address': 1, 'address_type': 'Billing', 'formatted_display': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607, zagazig, Egypt, 451147'}, 'phone_number': '01001733918', 'payment_method': 'Cash', 'items': [{'item_code': 'SKU006', 'item_name': 'Coffee Mug', 'qty': 1, 'rate': 0}], 'notes': '', 'tags': 'New Order'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-14 13:09:26,643 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-07-14', 'delivery_date': '2025-07-29', 'billing_address': {'name': 'mohamed amir-Billing', 'address_line1': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607', 'city': 'zagazig', 'country': 'Egypt', 'pincode': '451147', 'is_primary_address': 1, 'is_shipping_address': 1, 'address_type': 'Billing', 'formatted_display': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607, zagazig, Egypt, 451147'}, 'shipping_address': {'name': 'mohamed amir-Billing', 'address_line1': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607', 'city': 'zagazig', 'country': 'Egypt', 'pincode': '451147', 'is_primary_address': 1, 'is_shipping_address': 1, 'address_type': 'Billing', 'formatted_display': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607, zagazig, Egypt, 451147'}, 'phone_number': '01001733918', 'payment_method': 'Credit Card', 'items': [{'item_code': 'SKU006', 'item_name': 'Coffee Mug', 'qty': 1, 'rate': 0}], 'notes': '', 'tags': 'New Order'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-14 15:43:31,916 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-07-14', 'delivery_date': '2025-07-18', 'billing_address': {'name': 'mohamed amir-Billing', 'address_line1': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607', 'city': 'zagazig', 'country': 'Egypt', 'pincode': '451147', 'is_primary_address': 1, 'is_shipping_address': 1, 'address_type': 'Billing', 'formatted_display': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607, zagazig, Egypt, 451147'}, 'shipping_address': {'name': 'mohamed amir-Billing', 'address_line1': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607', 'city': 'zagazig', 'country': 'Egypt', 'pincode': '451147', 'is_primary_address': 1, 'is_shipping_address': 1, 'address_type': 'Billing', 'formatted_display': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607, zagazig, Egypt, 451147'}, 'phone_number': '01001733918', 'payment_method': 'Cheque', 'items': [{'item_code': 'SKU006', 'item_name': 'Coffee Mug', 'qty': 5, 'rate': 0}, {'item_code': 'SKU0030', 'item_name': 'Television1nihnihni', 'qty': 5, 'rate': 0}], 'notes': 'gdbbf', 'tags': 'New Order'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-14 15:47:21,063 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-07-14', 'delivery_date': '2025-07-30', 'billing_address': {'name': 'mohamed amir-Billing', 'address_line1': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607', 'city': 'zagazig', 'country': 'Egypt', 'pincode': '451147', 'is_primary_address': 1, 'is_shipping_address': 1, 'address_type': 'Billing', 'formatted_display': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607, zagazig, Egypt, 451147'}, 'shipping_address': {'name': 'mohamed amir-Billing', 'address_line1': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607', 'city': 'zagazig', 'country': 'Egypt', 'pincode': '451147', 'is_primary_address': 1, 'is_shipping_address': 1, 'address_type': 'Billing', 'formatted_display': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607, zagazig, Egypt, 451147'}, 'phone_number': '01001733918', 'payment_method': 'Wire Transfer', 'items': [{'item_code': 'SKU005', 'item_name': 'Sneakers12', 'qty': 3, 'rate': 0}, {'item_code': 'SKU002', 'item_name': 'Laptop', 'qty': 6, 'rate': 0}, {'item_code': 'SKU0030', 'item_name': 'Television1nihnihni', 'qty': 2, 'rate': 0}, {'item_code': 'SKU004', 'item_name': 'Smartphone', 'qty': 1, 'rate': 0}, {'item_code': 'SKU001', 'item_name': 'T-shirt', 'qty': 1, 'rate': 0}], 'notes': 'bjbkhjbhj', 'tags': 'New Order'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-14 15:53:17,177 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-07-14', 'delivery_date': '2025-07-16', 'billing_address': {'name': 'mohamed amir-Billing', 'address_line1': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607', 'city': 'zagazig', 'country': 'Egypt', 'pincode': '451147', 'is_primary_address': 1, 'is_shipping_address': 1, 'address_type': 'Billing', 'formatted_display': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607, zagazig, Egypt, 451147'}, 'shipping_address': {'name': 'mohamed amir-Billing', 'address_line1': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607', 'city': 'zagazig', 'country': 'Egypt', 'pincode': '451147', 'is_primary_address': 1, 'is_shipping_address': 1, 'address_type': 'Billing', 'formatted_display': 'محافظه الشرقه,حي ثان الزقازيق,مساكن القوميه,شارع القوميه,برج الضحي اعلي مول جهازي بجوار مدرسه القوميه , شقه 607, zagazig, Egypt, 451147'}, 'phone_number': '01001733918', 'payment_method': 'Wire Transfer', 'items': [{'item_code': 'SKU006', 'item_name': 'Coffee Mug', 'qty': 2, 'rate': 0}, {'item_code': 'SKU005', 'item_name': 'Sneakers12', 'qty': 1, 'rate': 0}], 'notes': '', 'tags': 'New Order'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-14 16:17:42,099 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-07-14', 'delivery_date': '2025-07-24', 'billing_address_id': 'mohamed amir-Billing', 'shipping_address_id': 'mohamed amir-Billing', 'contact_id': 'mohamed1545 amirec-mohamed amir', 'payment_method_id': 'Credit Card', 'items': [{'item_code': 'Sneakers12', 'item_name': 'Sneakers12', 'qty': 1, 'rate': 0}, {'item_code': 'Coffee Mug', 'item_name': 'Coffee Mug', 'qty': 3, 'rate': 0}], 'notes': 'brtg'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-14 16:34:11,892 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-14 16:38:19,450 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-07-14', 'delivery_date': '2025-07-28', 'billing_address_id': 'mohamed amir-Billing', 'shipping_address_id': 'mohamed amir-Billing', 'contact_id': 'mohamed1545 amirec-mohamed amir', 'payment_method_id': 'Cash', 'items': [{'item_code': 'Coffee Mug', 'item_name': 'Coffee Mug', 'qty': 1, 'rate': 0}, {'item_code': 'Sneakers12', 'item_name': 'Sneakers12', 'qty': 1, 'rate': 0}], 'notes': ''}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-14 16:42:14,143 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-07-14', 'delivery_date': '2025-07-28', 'billing_address_id': 'mohamed amir-Billing', 'shipping_address_id': 'mohamed amir-Billing', 'contact_id': 'mohamed1545 amirec-mohamed amir', 'payment_method_id': 'Cash', 'items': [{'item_code': 'Coffee Mug', 'item_name': 'Coffee Mug', 'qty': 1, 'rate': 0}, {'item_code': 'Sneakers12', 'item_name': 'Sneakers12', 'qty': 1, 'rate': 0}], 'notes': ''}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-14 16:45:11,180 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-07-14', 'delivery_date': '2025-07-28', 'billing_address_id': 'mohamed amir-Billing', 'shipping_address_id': 'mohamed amir-Billing', 'contact_id': 'mohamed1545 amirec-mohamed amir', 'payment_method_id': 'Cash', 'items': [{'item_code': 'Coffee Mug', 'item_name': 'Coffee Mug', 'qty': 1, 'rate': 0}, {'item_code': 'Sneakers12', 'item_name': 'Sneakers12', 'qty': 1, 'rate': 0}], 'notes': ''}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-14 16:46:24,585 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-07-14', 'delivery_date': '2025-07-28', 'billing_address_id': 'mohamed amir-Billing', 'shipping_address_id': 'mohamed amir-Billing', 'contact_id': 'mohamed1545 amirec-mohamed amir', 'payment_method_id': 'Cash', 'items': [{'item_code': 'Coffee Mug', 'item_name': 'Coffee Mug', 'qty': 1, 'rate': 0}, {'item_code': 'Sneakers12', 'item_name': 'Sneakers12', 'qty': 1, 'rate': 0}], 'notes': ''}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-14 16:54:39,943 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-07-14', 'delivery_date': '2025-07-18', 'billing_address_id': 'mohamed amir-Billing', 'shipping_address_id': 'mohamed amir-Billing', 'contact_id': 'mohamed1545 amirec-mohamed amir', 'payment_method_id': 'Cheque', 'items': [{'item_code': 'Sneakers12', 'item_name': 'Sneakers12', 'qty': 1, 'rate': 0}], 'notes': ''}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-14 16:54:52,966 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-07-14', 'delivery_date': '2025-07-18', 'billing_address_id': 'mohamed amir-Billing', 'shipping_address_id': 'mohamed amir-Billing', 'contact_id': 'mohamed1545 amirec-mohamed amir', 'payment_method_id': 'Cheque', 'items': [{'item_code': 'Sneakers12', 'item_name': 'Sneakers12', 'qty': 1, 'rate': 0}], 'notes': ''}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-14 16:55:53,930 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-07-14', 'delivery_date': '2025-07-24', 'billing_address_id': 'mohamed amir-Billing', 'shipping_address_id': 'mohamed amir-Billing', 'contact_id': 'mohamed1545 amirec-mohamed amir', 'payment_method_id': 'Cash', 'items': [{'item_code': 'Coffee Mug', 'item_name': 'Coffee Mug', 'qty': 1, 'rate': 0}], 'notes': ''}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-14 16:55:56,495 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-07-14', 'delivery_date': '2025-07-24', 'billing_address_id': 'mohamed amir-Billing', 'shipping_address_id': 'mohamed amir-Billing', 'contact_id': 'mohamed1545 amirec-mohamed amir', 'payment_method_id': 'Cash', 'items': [{'item_code': 'Coffee Mug', 'item_name': 'Coffee Mug', 'qty': 1, 'rate': 0}], 'notes': ''}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-14 16:59:09,321 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-07-14', 'delivery_date': '2025-07-24', 'billing_address_id': 'mohamed amir-Billing', 'shipping_address_id': 'mohamed amir-Billing', 'contact_id': 'mohamed1545 amirec-mohamed amir', 'payment_method_id': 'Cash', 'items': [{'item_code': 'Coffee Mug', 'item_name': 'Coffee Mug', 'qty': 1, 'rate': 0}], 'notes': ''}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-14 17:00:27,727 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-07-14', 'delivery_date': '2025-07-24', 'billing_address_id': 'mohamed amir-Billing', 'shipping_address_id': 'mohamed amir-Billing', 'contact_id': 'mohamed1545 amirec-mohamed amir', 'payment_method_id': 'Cash', 'items': [{'item_code': 'Coffee Mug', 'item_name': 'Coffee Mug', 'qty': 1, 'rate': 0}], 'notes': ''}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-14 17:00:37,899 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-07-14', 'delivery_date': '2025-07-24', 'billing_address_id': 'mohamed amir-Billing', 'shipping_address_id': 'mohamed amir-Billing', 'contact_id': 'mohamed1545 amirec-mohamed amir', 'payment_method_id': 'Cash', 'items': [{'item_code': 'Coffee Mug', 'item_name': 'Coffee Mug', 'qty': 1, 'rate': 0}], 'notes': ' jjlkknl'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-14 17:02:21,606 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-07-14', 'delivery_date': '2025-07-24', 'billing_address_id': 'mohamed amir-Billing', 'shipping_address_id': 'mohamed amir-Billing', 'contact_id': 'mohamed1545 amirec-mohamed amir', 'payment_method_id': 'Cash', 'items': [{'item_code': 'Coffee Mug', 'item_name': 'Coffee Mug', 'qty': 1, 'rate': 0}], 'notes': ' jjlkknl'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-14 17:07:10,321 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-07-14', 'delivery_date': '2025-07-25', 'billing_address_id': 'mohamed amir-Billing', 'shipping_address_id': 'mohamed amir-Billing', 'contact_id': 'mohamed1545 amirec-mohamed amir', 'payment_method_id': 'Cash', 'items': [{'item_code': 'Sneakers12', 'item_name': 'Sneakers12', 'qty': 1, 'rate': 0}], 'notes': ''}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-14 17:07:57,480 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-15 10:53:30,219 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-16 14:59:00,194 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-07-16', 'delivery_date': '2025-07-17', 'billing_address_id': 'mohamed amir-Billing', 'shipping_address_id': 'mohamed amir-Billing', 'contact_id': 'mohamed1545 amirec-mohamed amir', 'payment_method_id': 'Cheque', 'items': [{'item_code': 'SKU008', 'item_name': 'Backpack', 'qty': 1, 'rate': 500, 'warehouse': 'Stores - AFD'}, {'item_code': 'SKU003', 'item_name': 'Book', 'qty': 1, 'rate': 500, 'warehouse': 'Finished Goods - AFD'}], 'notes': ''}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-16 15:00:18,978 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-07-16', 'delivery_date': '2025-07-17', 'billing_address_id': 'mohamed amir-Billing', 'shipping_address_id': 'mohamed amir-Billing', 'contact_id': 'mohamed1545 amirec-mohamed amir', 'payment_method_id': 'Cheque', 'items': [{'item_code': 'SKU008', 'item_name': 'Backpack', 'qty': 1, 'rate': 500, 'warehouse': 'Stores - AFD'}, {'item_code': 'SKU003', 'item_name': 'Book', 'qty': 1, 'rate': 500, 'warehouse': 'Stores - AFD'}], 'notes': ''}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-16 15:01:47,675 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-07-16', 'delivery_date': '2025-07-19', 'billing_address_id': 'mohamed amir-Billing', 'shipping_address_id': 'mohamed amir-Billing', 'contact_id': 'mohamed1545 amirec-mohamed amir', 'payment_method_id': 'Cash', 'items': [{'item_code': 'SKU008', 'item_name': 'Backpack', 'qty': 1, 'rate': 500, 'warehouse': 'All Warehouses - AFD'}, {'item_code': 'SKU003', 'item_name': 'Book', 'qty': 1, 'rate': 500, 'warehouse': 'All Warehouses - AFD'}], 'notes': ''}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-16 15:04:19,918 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-07-16', 'delivery_date': '2025-07-18', 'billing_address_id': 'mohamed amir-Billing', 'shipping_address_id': 'mohamed amir-Billing', 'contact_id': 'mohamed1545 amirec-mohamed amir', 'payment_method_id': 'Cheque', 'items': [{'item_code': 'SKU008', 'item_name': 'Backpack', 'qty': 1, 'rate': 500, 'warehouse': 'All Warehouses - AFD'}, {'item_code': 'SKU003', 'item_name': 'Book', 'qty': 1, 'rate': 500, 'warehouse': 'All Warehouses - AFD'}], 'notes': ''}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-16 15:06:48,150 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-07-16', 'delivery_date': '2025-07-18', 'billing_address_id': 'mohamed amir-Billing', 'shipping_address_id': 'mohamed amir-Billing', 'contact_id': 'mohamed1545 amirec-mohamed amir', 'payment_method_id': 'Cheque', 'items': [{'item_code': 'SKU008', 'item_name': 'Backpack', 'qty': 1, 'rate': 500, 'warehouse': 'All Warehouses - AFD'}, {'item_code': 'SKU003', 'item_name': 'Book', 'qty': 1, 'rate': 500, 'warehouse': 'All Warehouses - AFD'}], 'notes': ''}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-16 15:11:12,723 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'customer': 'mohamed amir', 'customer_name': 'mohamed amir', 'transaction_date': '2025-07-16', 'delivery_date': '2025-07-18', 'billing_address_id': 'mohamed amir-Billing', 'shipping_address_id': 'mohamed amir-Billing', 'contact_id': 'mohamed1545 amirec-mohamed amir', 'payment_method_id': 'Cheque', 'items': [{'item_code': 'SKU008', 'item_name': 'Backpack', 'qty': 1, 'rate': 500, 'warehouse': 'All Warehouses - AFD'}, {'item_code': 'SKU003', 'item_name': 'Book', 'qty': 1, 'rate': 500, 'warehouse': 'All Warehouses - AFD'}], 'notes': ''}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-16 15:21:37,767 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-17 18:02:54,325 ERROR frappe Failed to run after request hook
Site: parentngo
Form Dict: {'cmd': 'login', 'usr': 'Administrator', 'pwd': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-21 12:40:27,662 ERROR frappe Failed to run after request hook
Site: parentngo
Form Dict: {'cmd': 'login', 'usr': 'Administrator', 'pwd': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-21 12:40:31,329 ERROR frappe Failed to run after request hook
Site: parentngo
Form Dict: {'cmd': 'login', 'usr': 'Administrator', 'pwd': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-21 12:40:35,525 ERROR frappe Failed to run after request hook
Site: parentngo
Form Dict: {'cmd': 'login', 'usr': 'Administrator', 'pwd': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-23 12:06:03,307 ERROR frappe Failed to run after request hook
Site: parentngo
Form Dict: {'cmd': 'login', 'usr': 'Administrator', 'pwd': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-28 16:15:57,567 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'cmd': 'nexus.api.v1.system.endpoints.get_default_currency'}
2025-07-28 16:37:44,171 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-28 16:38:15,951 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'first_name': 'mohamed', 'last_name': 'amir', 'email': '<EMAIL>', 'password': '********', 'company_name': 'asdf', 'abbr': 'Adfss', 'country': 'Egypt', 'default_currency': 'EGP', 'language': 'ar'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-28 16:38:47,874 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'first_name': 'mohamed', 'last_name': 'amir', 'email': '<EMAIL>', 'password': '********', 'company_name': 'asdf', 'abbr': 'Adfss', 'country': 'Egypt', 'default_currency': 'EGP', 'language': 'ar'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 163, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1754, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
